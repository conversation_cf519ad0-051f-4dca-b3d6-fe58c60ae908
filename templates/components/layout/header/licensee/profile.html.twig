{# @var licensee \App\Entity\Licensee #}
{# @var company \App\Entity\Company #}
{% set licensee = licensee_finder.find %}
{% set company = licensee.company %}

{% set edit_profile_url = url('app_account_profile_edit') %}
{% set profile_page_url = url('app_account_profile_view') %}

<div class="card-popup w-xl">
    {# Top section: Logo and Logout #}
    <div class="flex justify-between items-center w-full">
        <img src="{{ company.logo }}" alt="{{ company.name }} Logo">
        <a href="{{ url('app_logout') }}" class="btn-text">
            <span>Log Out</span>
            <twig:ux:icon name="mdi:logout"/>
        </a>
    </div>

    <h2 class="mt-2">{{ company.name }}</h2>
    <div class="text-primary-text">
        <span>{{ company.country.name }}</span>
        <a href="{{ company.website }}" target="_blank" rel="noopener noreferrer" class="btn-text">
            <span>{{ company.website | replace({'https://': '', 'http://': ''}) }}</span>
            <twig:ux:icon name="mdi:arrow-top-right" class="w-4 h-4"/>
        </a>
    </div>

    {# Company Info & Description #}
    <p>{{ company.description }}</p>

    {# Favorited Projects Section #}
    <div class="card-accent flex my-5 p-2">
        <twig:ux:icon name="mdi:heart-outline" class="bg-accent-background rounded-lg h-12 p-1 mr-4"/>

        <div class="flex flex-col justify-start">
            <span class="text-primary-text">Favoured projects</span>
            <span class="text-black font-semibold">{{ licensee.favoriteProjects.count() }}</span>
        </div>

        <a href="{{ url('app_account_licensee_favorite_projects') }}" class="btn-text ml-auto">
            <span>View all</span>
            <twig:ux:icon name="mdi:arrow-right"/>
        </a>
    </div>

    {# Badge Section #}
    {% set hideAfter = 2 %}
    <div class="grid grid-cols-3 gap-4">
        <twig:layout:badge-list
                label="Products"
                values="{{ licensee.productCategories }}"
                hideAfter="{{ hideAfter }}"
        />
        <twig:layout:badge-list
                label="Categories"
                values="{{ licensee.ipCategoryInterests }}"
                hideAfter="{{ hideAfter }}"
        />
        <twig:layout:badge-list
                label="Territories"
                values="{{ licensee.territories }}"
                class="flex-1/3"
                hideAfter="{{ hideAfter }}"
        />
        <twig:layout:badge-list
                label="Age groups"
                values="{{ licensee.targetAudienceAgeGroups }}"
                hideAfter="{{ hideAfter }}"
        />
        <twig:layout:badge-list
                label="Genders"
                values="{{ licensee.targetAudienceGenders }}"
                hideAfter="{{ hideAfter }}"
        />
    </div>

    {# Actions Section #}
    <div class="flex items-center gap-2 mt-4">
        <a href="{{ edit_profile_url }}" class="btn-primary">Edit profile</a>
        <a href="{{ profile_page_url }}" class="btn-text">
            <span>See profile page</span>
            <twig:ux:icon name="mdi:arrow-right"/>
        </a>
    </div>
</div>
