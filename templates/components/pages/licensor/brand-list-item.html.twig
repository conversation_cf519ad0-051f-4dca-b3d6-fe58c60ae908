{# @var brand \App\Entity\Brand #}
<div {{ attributes }} class="contents" data-testid="brand-list-item">
    <div class="card border border-stroke h-full flex flex-col">
        <div class="py-8 font-semibold text-sm">
            <span>{{ brand.name }}</span>
            <span class="float-right">By {{ brand.licensor.company.name }}
                    <img
                        class="ml-2 inline"
                        src="{{ brand.licensor.company.logo }}"
                        width="28px"
                        height="28px"
                        title="{{ brand.licensor.company.name }}"
                        alt="{{ brand.licensor.company.name }}"
                    />
                </span>
        </div>
        <a class="max-h-40 w-full" href="{{ url('app_licensor_brand_details', {slug: brand.slug}) }}">
            <img src="{{ brand.heroImage }}" alt="{{ brand.name }}"/>
        </a>

        <div class="mt-8">
            <twig:layout:badge-list
                    label="IP Categories"
                    itemClass="badge bg-brand-light text-primary-text/80"
                    values="{{ brand.ipCategories }}"
                    hideAfter="4"
            />
        </div>
        <div class="my-8">
            <twig:layout:badge-list
                    label="Territories"
                    itemClass="badge bg-brand-light text-primary-text/80"
                    values="{{ this.getBrandTerritories() }}"
                    hideAfter="4"
            />
        </div>
        <div class="mt-auto">
            <div data-controller="dialog" data-action="click->dialog#backdropClose">
                <button data-action="dialog#open" class="btn-secondary w-full">
                    Explore opportunities
                </button>

                <dialog data-dialog-target="dialog" class="w-5xl relative">
                    <twig:ux:icon name="mdi:close" class="w-8 h-8 absolute top-2 right-2" data-action="click->dialog#close" autofocus/>

                    <h2>Brand opportunities</h2>
                    <twig:pages:licensor:brand-opportunity-list brandId="{{ brand.id }}" loading="lazy"/>
                </dialog>
            </div>
        </div>
    </div>
</div>
