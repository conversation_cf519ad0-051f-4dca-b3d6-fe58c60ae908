{# @var result \App\Interfaces\MatchItemInterface #}
{% set result = this.get() %}
{% set type = this.getType(result) %}
{% set baseItem = this.getBaseItem() %}
<div {{ attributes }} class="flex flex-col gap-8 bg-white rounded-4xl p-1 max-w-6xl mx-auto relative" data-test-id="brand-match-container">

    <div class="relative text-accent text-shadow-md ">
        <img src="{{ result.heroImage }}"
             alt="{{ result.name }}"
             title="{{ result.name }}"
             class="w-full max-h-130 block rounded-4xl"/>

        <div class="absolute top-0 left-0 w-full flex flex-col gap-12 p-6">
            <div class="flex flex-row justify-between">
                <h1>{{ result.name }}</h1>

                {% if type == 'App\\Entity\\Project' %}
                    <twig:pages:licensor:project-bid-button project="{{ result }}"/>
                {% endif %}
                {% if type == 'App\\Entity\\Brand' %}
                    <twig:pages:licensor:brand-pitch-button brand="{{ result }}"/>
                {% endif %}
                {% if type is same as('App\\Entity\\Licensee') %}
                    {#
                    <twig:pages:licensee:licensor-bid-button licensor="{{ result }}"/>
                    #}
                {% endif %}
            </div>

            <div class="font-medium text-base">
                <img src="{{ asset('assets/images/fire.png') }}" alt="Match" class="h-6 inline" />

                {# TODO: Implement actual calculation of matched / total points #}
                <span>{{ "6" }} / {{ "6" }}</span> points matched
            </div>
        </div>

        {% if (this.index() > 0) %}
            <button class="absolute top-1/2 left-6" {{ live_action('prev') }} data-test-id="prev-button">
                <twig:ux:icon name="mdi:chevron-left" class="w-14"/>
            </button>
        {% endif %}
        <button class="absolute top-1/2 right-6" {{ live_action('next', {'itemId': result.id, 'itemType': type}) }} data-test-id="next-button">
            <twig:ux:icon name="mdi:chevron-right" class="w-14"/>
        </button>
    </div>

    <div class="flex flex-row p-6 gap-8">
        <div class="flex-1/3 flex flex-col justify-between">
            <p >{{ result.description }}</p>

            <a class="py-2"
               href="{{ this.getDetailUrl(result) }}"
               title="{{ result.name }}">
                <button class="btn-text">
                    <span class="text-neutral font-semibold">Visit {{ result.name }}</span>
                    <twig:ux:icon name="mdi:arrow-right"/>
                </button>
            </a>
        </div>

        <div class="flex-2/3">
            <p class="text-neutral mb-6">You align on the following criteria</p>
            <div class="grid grid-cols-3 gap-4">
                <twig:layout:match-item-alignment base="{{ baseItem }}" against="{{ result }}"/>
            </div>
        </div>
    </div>


    <div class="flex gap-4">
        <div class="w-1/2 flex-none relative">
            <button class="btn btn-dark-gray left-1/2 -translate-x-1/2 absolute" {{ live_action('next', {'itemId': result.id, 'itemType': type}) }} data-test-id="skip-button">
                <twig:ux:icon name="skip-emoji"/>
                <span class="text-2xl">Skip</span>
            </button>
        </div>
        <div class="w-1/2 flex-none relative">
            {% if type == 'App\\Entity\\Project' %}
                <twig:pages:licensor:project-favorite-button projectId="{{ result.id }}" class="btn bg-accent rounded-full p-8 text-2xl"/>
            {% endif %}
            {% if type == 'App\\Entity\\Brand' %}
                <twig:pages:licensor:brand-favorite-button brandId="{{ result.id }}" class="btn bg-accent rounded-full p-8 text-2xl"/>
            {% endif %}
            {% if type == 'App\\Entity\\Licensee' %}
                {# TODO #}
            {% endif %}
        </div>
    </div>
</div>
