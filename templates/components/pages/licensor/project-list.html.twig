{% set projectListItems = this.projectListItems %}
<div {{ attributes }}>
    <twig:pages:licensor:list-filters form="{{ form }}" clearUrl="{{ url('app_licensor_project_list') }}"/>

    {% if projectListItems is empty %}
        <div class="p-4 bg-white shadow rounded-lg">
            <p class="text-gray-700">No projects match your criteria</p>
        </div>
    {% else %}
        <div class="flex flex-col gap-4">
            {% for projectListItem in projectListItems %}
                {% set project = projectListItem.project %}
                {% set htmlId = 'project-list-item-' ~ project.id %}
                {% set filtered = this.isFiltered() %}

                <twig:pages:licensor:project-list-item project="{{ project }}" id="{{ htmlId }}">

                    <twig:block name="buttons">
                        {% if projectListItem.hasSimilarProjects %}
                            <button class="btn-outline" data-testid="similar-projects-button"
                                    data-action="extended-reveal#toggle">
                                <span data-extended-reveal-target="showButton">See similar projects</span>
                                <span data-extended-reveal-target="hideButton" class="hidden">Hide similar projects</span>
                            </button>
                        {% endif %}

                        {% if filtered %}
                            <a href="{{ url('app_licensor_project_list') }}" class="btn-text w-full"
                               title="See all opportunities" data-testid="show-all-projects-button">
                                <button class="btn-text">
                                    <span class="text-neutral font-semibold">See all opportunities</span>
                                    <twig:ux:icon name="mdi:arrow-right"/>
                                </button>
                            </a>
                        {% endif %}
                    </twig:block>
                </twig:pages:licensor:project-list-item>
            {% endfor %}
        </div>

        {{ knp_pagination_render(projectListItems) }}

    {% endif %}
</div>
