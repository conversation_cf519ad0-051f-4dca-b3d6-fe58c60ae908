<div
        {{ stimulus_controller('extended-reveal') }}
        class="project-list-item"
        data-extended-reveal-target="container"
>
    <div class="card w-full flex gap-4 relative" data-testid="project-list-item">
        <div class="flex-1/3 flex flex-col gap-6">
            <div>
                <h5 class="text-neutral">{{ project.brand.name }}</h5>
                <a href="{{ url('app_licensor_brand_details', {slug: project.brand.slug}) }}">
                    <img src="{{ project.brand.logo }}" alt="{{ project.brand.name }}"/>
                </a>
            </div>

            <div>
                <h5 class="text-neutral">Product</h5>
                <a
                    class="text-2xl font-semibold"
                    href="{{ url('app_licensor_project_details', {slug: project.slug}) }}"
                >
                    {{ project.name }}
                </a>
            </div>

            <p class="mt-auto">{{ project.description }}</p>
        </div>
        <div class="flex-grow">
            <h5 class="text-neutral">{{ project.brand.licensor.company.name }}</h5>
            <img src="{{ project.brand.licensor.company.logo }}"
                 alt="{{ project.brand.licensor.company.name }}"/>

            <p class="text-neutral my-4">You align on the following criteria</p>

            {% set licensee = licensee_finder.find %}
            <div class="grid grid-cols-3 gap-4">
                <twig:layout:match-item-alignment base="{{ licensee }}" against="{{ project }}"/>
            </div>
        </div>

        <div class="flex-shrink flex flex-col justify-between">
            <twig:pages:licensor:project-favorite-button projectId="{{ project.id }}" class="text-right"/>

            <div class="flex flex-col space-y-1 justify-items-stretch">
                <twig:pages:licensor:project-bid-button project="{{ project }}"/>

                {% block buttons %}{% endblock %}
            </div>
        </div>
    </div>

    <div class="hidden" data-extended-reveal-target="item">
        <twig:pages:licensor:similar-project-list projectId="{{ project.id }}" loading="lazy"/>
    </div>
</div>
